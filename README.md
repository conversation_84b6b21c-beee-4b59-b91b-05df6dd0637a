# AI Travel Planner

An intelligent Flutter app that helps users plan their perfect trips using AI assistance, Google Maps integration, and Firebase backend.

## Features

- 🤖 AI-powered trip planning with Gemini AI
- 🗺️ Interactive maps and location search with Google Places
- 📅 Flexible date range selection
- 💰 Smart budget management
- 🔐 Firebase authentication and data storage
- 📱 Beautiful, responsive UI with Google Fonts

## Setup Instructions

### Prerequisites

- Flutter SDK (latest stable version)
- Android Studio / VS Code with Flutter extensions
- Firebase account
- Google Cloud Platform account

### 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd ai_travel_planner
flutter pub get
```

### 2. Firebase Configuration

1. Create a new Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Enable Authentication, Firestore Database
3. Download the configuration files:
   - For Android: `google-services.json` → `android/app/`
   - For iOS: `GoogleService-Info.plist` → `ios/Runner/`
4. Update `lib/config/firebase_config.dart` with your Firebase project details

### 3. API Keys Configuration

You need to obtain the following API keys:

#### Google Places API Key
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Enable Places API
3. Create credentials (API Key)
4. Restrict the key to Places API

#### Google Maps API Key
1. In the same Google Cloud project
2. Enable Maps SDK for Android/iOS
3. Create credentials (API Key)
4. Restrict the key to Maps SDK

#### Gemini AI API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key for configuration

#### Update Configuration
Edit `lib/config/api_config.dart` and replace the placeholder values:

```dart
static const String googlePlacesApiKey = 'your-actual-google-places-api-key';
static const String googleMapsApiKey = 'your-actual-google-maps-api-key';
static const String geminiApiKey = 'your-actual-gemini-api-key';
```

### 4. Run the App

```bash
flutter run
```

## Development Phases

This project is being developed in phases:

- ✅ **Phase 1**: Project Setup and Basic Configuration
- 🔄 **Phase 2**: Authentication and Basic UI Structure
- ⏳ **Phase 3**: Travel Planning Flow Screens
- ⏳ **Phase 4**: Location and AI Integration
- ⏳ **Phase 5**: Trip Details and Review
- ⏳ **Phase 6**: Data Management and Storage
- ⏳ **Phase 7**: Polish and Optimization
- ⏳ **Phase 8**: Testing and Deployment

## Testing

Run the test suite:

```bash
flutter test
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
