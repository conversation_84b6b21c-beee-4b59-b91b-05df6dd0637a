# AI Travel Planner

An intelligent Flutter app that helps users plan their perfect trips using AI assistance, Google Maps integration, and Firebase backend.

## Features

- 🤖 AI-powered trip planning with Gemini AI
- 🗺️ Interactive maps and location search with Google Places
- 📅 Flexible date range selection
- 💰 Smart budget management
- 🔐 Firebase authentication and data storage
- 📱 Beautiful, responsive UI with Google Fonts

## Setup Instructions

### Prerequisites

- Flutter SDK (latest stable version)
- Android Studio / VS Code with Flutter extensions
- Firebase account
- Google Cloud Platform account

### 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd ai_travel_planner
flutter pub get
```

### 2. Firebase Configuration

1. Create a new Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Enable Authentication, Firestore Database
3. Download the configuration files:
   - For Android: `google-services.json` → `android/app/`
   - For iOS: `GoogleService-Info.plist` → `ios/Runner/`
4. Update `lib/config/firebase_config.dart` with your Firebase project details

### 3. Environment Variables Setup

Create a `.env` file in the project root (copy from `.env.example`):

```bash
cp .env.example .env
```

### 4. API Keys Configuration

You need to obtain the following API keys and add them to your `.env` file:

#### Google API Key (Recommended: Single Key)
**The Google Places API key and Google Maps API key can be the same key!**

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the following APIs:
   - **Places API**
   - **Maps SDK for Android**
   - **Maps SDK for iOS**
   - **Geocoding API** (optional)
4. Go to **Credentials** > **Create Credentials** > **API Key**
5. Restrict the key to the APIs listed above
6. Add to your `.env` file:

```env
# Single key for both Places and Maps (recommended)
GOOGLE_API_KEY=your-google-api-key-here
```

#### Alternative: Separate Keys
If you prefer separate keys for security:

```env
GOOGLE_PLACES_API_KEY=your-google-places-api-key-here
GOOGLE_MAPS_API_KEY=your-google-maps-api-key-here
```

#### Gemini AI API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add to your `.env` file:

```env
GEMINI_API_KEY=your-gemini-api-key-here
```

#### Firebase Configuration
Get these values from your Firebase project settings and add to `.env`:

```env
FIREBASE_API_KEY=your-firebase-api-key-here
FIREBASE_APP_ID=your-firebase-app-id-here
FIREBASE_MESSAGING_SENDER_ID=your-firebase-messaging-sender-id-here
FIREBASE_PROJECT_ID=your-firebase-project-id-here
```

### 5. Run the App

```bash
flutter run
```

## Development Phases

This project is being developed in phases:

- ✅ **Phase 1**: Project Setup and Basic Configuration
- 🔄 **Phase 2**: Authentication and Basic UI Structure
- ⏳ **Phase 3**: Travel Planning Flow Screens
- ⏳ **Phase 4**: Location and AI Integration
- ⏳ **Phase 5**: Trip Details and Review
- ⏳ **Phase 6**: Data Management and Storage
- ⏳ **Phase 7**: Polish and Optimization
- ⏳ **Phase 8**: Testing and Deployment

## Testing

Run the test suite:

```bash
flutter test
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
