import 'package:flutter_dotenv/flutter_dotenv.dart';

/// API configuration class for the AI Travel Planner app
/// Uses environment variables for secure API key management
class ApiConfig {
  /// Google API key (can be used for both Places and Maps)
  static String get googleApiKey => dotenv.env['GOOGLE_API_KEY'] ?? '';

  /// Google Places API key (falls back to general Google API key)
  static String get googlePlacesApiKey =>
      dotenv.env['GOOGLE_PLACES_API_KEY'] ?? googleApiKey;

  /// Google Maps API key (falls back to general Google API key)
  static String get googleMapsApiKey =>
      dotenv.env['GOOGLE_MAPS_API_KEY'] ?? googleApiKey;

  /// Gemini AI API key
  static String get geminiApiKey => dotenv.env['GEMINI_API_KEY'] ?? '';

  /// Validate that all required API keys are configured
  static bool areApiKeysConfigured() {
    return googlePlacesApiKey.isNotEmpty &&
        googleMapsApiKey.isNotEmpty &&
        geminiApiKey.isNotEmpty;
  }

  /// Get a list of missing API keys
  static List<String> getMissingApiKeys() {
    List<String> missing = [];

    if (googlePlacesApiKey.isEmpty) {
      missing.add(
        'Google Places API Key (GOOGLE_API_KEY or GOOGLE_PLACES_API_KEY)',
      );
    }

    if (googleMapsApiKey.isEmpty) {
      missing.add(
        'Google Maps API Key (GOOGLE_API_KEY or GOOGLE_MAPS_API_KEY)',
      );
    }

    if (geminiApiKey.isEmpty) {
      missing.add('Gemini AI API Key (GEMINI_API_KEY)');
    }

    return missing;
  }

  /// Check if using a single Google API key for both services
  static bool isUsingSingleGoogleKey() {
    return dotenv.env['GOOGLE_API_KEY']?.isNotEmpty == true &&
        dotenv.env['GOOGLE_PLACES_API_KEY']?.isEmpty != false &&
        dotenv.env['GOOGLE_MAPS_API_KEY']?.isEmpty != false;
  }
}
