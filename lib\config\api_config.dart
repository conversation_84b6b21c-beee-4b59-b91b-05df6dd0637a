/// API configuration class for the AI Travel Planner app
class ApiConfig {
  // TODO: Replace with your actual API keys
  // IMPORTANT: In production, these should be stored securely (e.g., environment variables)
  
  /// Google Places API key
  static const String googlePlacesApiKey = 'your-google-places-api-key-here';
  
  /// Google Maps API key
  static const String googleMapsApiKey = 'your-google-maps-api-key-here';
  
  /// Gemini AI API key
  static const String geminiApiKey = 'your-gemini-api-key-here';
  
  /// Validate that all required API keys are configured
  static bool areApiKeysConfigured() {
    return googlePlacesApiKey != 'your-google-places-api-key-here' &&
           googleMapsApiKey != 'your-google-maps-api-key-here' &&
           geminiApiKey != 'your-gemini-api-key-here';
  }
  
  /// Get a list of missing API keys
  static List<String> getMissingApiKeys() {
    List<String> missing = [];
    
    if (googlePlacesApiKey == 'your-google-places-api-key-here') {
      missing.add('Google Places API Key');
    }
    
    if (googleMapsApiKey == 'your-google-maps-api-key-here') {
      missing.add('Google Maps API Key');
    }
    
    if (geminiApiKey == 'your-gemini-api-key-here') {
      missing.add('Gemini AI API Key');
    }
    
    return missing;
  }
}
