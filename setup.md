# Quick Setup Guide

## 1. Copy Environment File
```bash
cp .env.example .env
```

## 2. Get Your API Keys

### Google API Key (Single Key - Recommended)
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable these APIs:
   - Places API
   - Maps SDK for Android
   - Maps SDK for iOS
4. Create API Key in Credentials
5. Add to `.env`:
   ```
   GOOGLE_API_KEY=your-actual-key-here
   ```

### Gemini AI API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create API key
3. Add to `.env`:
   ```
   GEMINI_API_KEY=your-actual-key-here
   ```

### Firebase Configuration
1. Create Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Enable Authentication and Firestore
3. Get config from Project Settings
4. Add to `.env`:
   ```
   FIREBASE_API_KEY=your-firebase-api-key
   FIREBASE_APP_ID=your-app-id
   FIREBASE_MESSAGING_SENDER_ID=your-sender-id
   FIREBASE_PROJECT_ID=your-project-id
   ```

## 3. Run the App
```bash
flutter pub get
flutter run
```

## 4. Verify Setup
- App should show "Configuration Required" if keys are missing
- Once configured, the "Start Planning Your Trip" button will be enabled

## API Key Security Notes
- Never commit `.env` file to version control
- Use separate keys for production
- Restrict API keys to specific services
- Consider using Firebase App Check for additional security
