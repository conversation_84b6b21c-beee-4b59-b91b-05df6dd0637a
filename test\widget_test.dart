// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:provider/provider.dart';

import 'package:ai_travel_planner/main.dart';
import 'package:ai_travel_planner/services/auth_service.dart';
import 'package:ai_travel_planner/screens/auth/login_screen.dart';

void main() {
  setUpAll(() async {
    // Initialize dotenv for tests with empty values
    dotenv.testLoad(
      fileInput: '''
GOOGLE_API_KEY=
GOOGLE_PLACES_API_KEY=
GOOGLE_MAPS_API_KEY=
GEMINI_API_KEY=
FIREBASE_API_KEY=
FIREBASE_APP_ID=
FIREBASE_MESSAGING_SENDER_ID=
FIREBASE_PROJECT_ID=
''',
    );
  });

  testWidgets('Login Screen displays correctly', (WidgetTester tester) async {
    // Build the login screen with provider
    await tester.pumpWidget(
      ChangeNotifierProvider(
        create: (_) => AuthService(),
        child: MaterialApp(home: const LoginScreen()),
      ),
    );

    // Verify that the login screen elements are displayed
    expect(find.text('Welcome Back'), findsOneWidget);
    expect(
      find.text('Sign in to continue planning your trips'),
      findsOneWidget,
    );
    expect(find.text('Email'), findsWidgets);
    expect(find.text('Password'), findsWidgets);
    expect(find.text('Sign In'), findsOneWidget);
    expect(find.text('Forgot Password?'), findsOneWidget);
    expect(find.text('Sign Up'), findsOneWidget);
  });

  testWidgets('TravelPlannerApp builds without errors', (
    WidgetTester tester,
  ) async {
    // Just test that the app can be created without errors
    const app = TravelPlannerApp();
    expect(app, isA<Widget>());
  });
}
