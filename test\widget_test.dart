// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

import 'package:ai_travel_planner/main.dart';

void main() {
  setUpAll(() async {
    // Initialize dotenv for tests with empty values
    dotenv.testLoad(
      fileInput: '''
GOOGLE_API_KEY=
GOOGLE_PLACES_API_KEY=
GOOGLE_MAPS_API_KEY=
GEMINI_API_KEY=
FIREBASE_API_KEY=
FIREBASE_APP_ID=
FIREBASE_MESSAGING_SENDER_ID=
FIREBASE_PROJECT_ID=
''',
    );
  });

  testWidgets('Travel Planner App loads correctly', (
    WidgetTester tester,
  ) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const TravelPlannerApp());

    // Wait for the app to settle
    await tester.pumpAndSettle();

    // Verify that the app title is displayed.
    expect(find.text('AI Travel Planner'), findsOneWidget);
    expect(find.text('Welcome to AI Travel Planner'), findsOneWidget);

    // Verify that the main action button is present.
    expect(find.text('Start Planning Your Trip'), findsOneWidget);

    // Verify that configuration warning is shown (since API keys are not configured)
    expect(find.text('Configuration Required'), findsOneWidget);

    // Verify that the .env setup message is shown
    expect(
      find.textContaining('Please configure your .env file'),
      findsOneWidget,
    );
  });
}
