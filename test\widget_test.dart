// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:ai_travel_planner/main.dart';

void main() {
  testWidgets('Travel Planner App loads correctly', (
    WidgetTester tester,
  ) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const TravelPlannerApp());

    // Wait for the app to settle
    await tester.pumpAndSettle();

    // Verify that the app title is displayed.
    expect(find.text('AI Travel Planner'), findsOneWidget);
    expect(find.text('Welcome to AI Travel Planner'), findsOneWidget);

    // Verify that the main action button is present.
    expect(find.text('Start Planning Your Trip'), findsOneWidget);

    // Verify that configuration warning is shown (since API keys are not configured)
    expect(find.text('Configuration Required'), findsOneWidget);

    // Verify that some feature cards are displayed (they might be in a scrollable view)
    expect(find.text('AI Planning'), findsOneWidget);
  });
}
