import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Firebase configuration class for the AI Travel Planner app
/// Uses environment variables for secure configuration management
class FirebaseConfig {
  /// Initialize Firebase with platform-specific options
  static Future<void> initialize() async {
    await Firebase.initializeApp(options: _getFirebaseOptions());
  }

  /// Get platform-specific Firebase options from environment variables
  static FirebaseOptions _getFirebaseOptions() {
    return FirebaseOptions(
      apiKey: dotenv.env['FIREBASE_API_KEY'] ?? '',
      appId: dotenv.env['FIREBASE_APP_ID'] ?? '',
      messagingSenderId: dotenv.env['FIREBASE_MESSAGING_SENDER_ID'] ?? '',
      projectId: dotenv.env['FIREBASE_PROJECT_ID'] ?? '',
      authDomain: dotenv.env['FIREBASE_AUTH_DOMAIN'],
      storageBucket: dotenv.env['FIREBASE_STORAGE_BUCKET'],
      measurementId: dotenv.env['FIREBASE_MEASUREMENT_ID'],
    );
  }

  /// Check if Firebase configuration is complete
  static bool isConfigured() {
    return dotenv.env['FIREBASE_API_KEY']?.isNotEmpty == true &&
        dotenv.env['FIREBASE_APP_ID']?.isNotEmpty == true &&
        dotenv.env['FIREBASE_MESSAGING_SENDER_ID']?.isNotEmpty == true &&
        dotenv.env['FIREBASE_PROJECT_ID']?.isNotEmpty == true;
  }

  /// Get list of missing Firebase configuration values
  static List<String> getMissingConfig() {
    List<String> missing = [];

    if (dotenv.env['FIREBASE_API_KEY']?.isEmpty != false) {
      missing.add('FIREBASE_API_KEY');
    }

    if (dotenv.env['FIREBASE_APP_ID']?.isEmpty != false) {
      missing.add('FIREBASE_APP_ID');
    }

    if (dotenv.env['FIREBASE_MESSAGING_SENDER_ID']?.isEmpty != false) {
      missing.add('FIREBASE_MESSAGING_SENDER_ID');
    }

    if (dotenv.env['FIREBASE_PROJECT_ID']?.isEmpty != false) {
      missing.add('FIREBASE_PROJECT_ID');
    }

    return missing;
  }
}
