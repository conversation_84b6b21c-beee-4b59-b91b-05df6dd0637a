import 'package:firebase_core/firebase_core.dart';

/// Firebase configuration class for the AI Travel Planner app
class FirebaseConfig {
  /// Initialize Firebase with platform-specific options
  static Future<void> initialize() async {
    await Firebase.initializeApp(
      options: _getFirebaseOptions(),
    );
  }

  /// Get platform-specific Firebase options
  static FirebaseOptions _getFirebaseOptions() {
    // TODO: Replace with your actual Firebase configuration
    // You can get these values from your Firebase project settings
    return const FirebaseOptions(
      apiKey: 'your-api-key-here',
      appId: 'your-app-id-here',
      messagingSenderId: 'your-sender-id-here',
      projectId: 'your-project-id-here',
      // Add other platform-specific options as needed
    );
  }
}
