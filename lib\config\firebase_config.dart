import 'package:firebase_core/firebase_core.dart';

/// Firebase configuration class for the AI Travel Planner app
/// Uses FlutterFire CLI generated configuration
class FirebaseConfig {
  /// Initialize Firebase with platform-specific options
  static Future<void> initialize() async {
    try {
      // Import the generated firebase_options.dart file
      // This will be created by running: flutterfire configure
      // Uncomment the next line after running flutterfire configure:
      // await Firebase.initializeApp(
      //   options: DefaultFirebaseOptions.currentPlatform,
      // );

      // For now, initialize without options (will use default)
      await Firebase.initializeApp();
    } catch (e) {
      // If firebase_options.dart doesn't exist yet, Firebase won't initialize
      // This is expected before running flutterfire configure
      throw Exception(
        'Firebase not configured. Please run "flutterfire configure" to set up Firebase for your Flutter app.',
      );
    }
  }

  /// Check if Firebase is initialized
  static bool get isInitialized {
    try {
      return Firebase.apps.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Check if Firebase configuration is complete
  static bool isConfigured() {
    // After running flutterfire configure, this will always be true
    // The configuration is handled by the generated firebase_options.dart
    return isInitialized;
  }

  /// Get list of missing Firebase configuration values
  static List<String> getMissingConfig() {
    if (!isInitialized) {
      return ['Firebase not configured. Run "flutterfire configure"'];
    }
    return [];
  }
}
